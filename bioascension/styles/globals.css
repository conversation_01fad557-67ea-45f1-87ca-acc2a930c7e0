@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  @apply font-sans;
}

/* Custom DNA Animation */
@keyframes dna-rotate {
  0% { transform: rotateY(0deg) rotateX(0deg); }
  25% { transform: rotateY(90deg) rotateX(15deg); }
  50% { transform: rotateY(180deg) rotateX(0deg); }
  75% { transform: rotateY(270deg) rotateX(-15deg); }
  100% { transform: rotateY(360deg) rotateX(0deg); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(0, 194, 168, 0.3); }
  50% { box-shadow: 0 0 40px rgba(0, 194, 168, 0.6); }
}

@keyframes particle-orbit {
  0% { transform: translate(-50%, -50%) rotate(0deg) translateY(-120px) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg) translateY(-120px) rotate(-360deg); }
}

.dna-helix {
  animation: dna-rotate 15s linear infinite;
}

.floating-element {
  animation: float 3s ease-in-out infinite;
}

.glow-effect {
  animation: glow 2s ease-in-out infinite alternate;
}

.particle-orbit {
  animation: particle-orbit 8s linear infinite;
}