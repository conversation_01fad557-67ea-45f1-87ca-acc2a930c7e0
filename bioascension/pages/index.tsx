import Head from 'next/head';
import { useState } from 'react';

export default function Home() {
  const [openFaq, setOpenFaq] = useState(null);
  const faqs = [
    {
      question: 'Is this accurate without a DNA test?',
      answer: 'Yes — we trained on thousands of data points. Our accuracy is >95%.'
    },
    {
      question: 'Do I need a doctor?',
      answer: 'Nope. You just answer questions, our systems deliver.'
    },
    {
      question: 'Can this help with looksmaxing?',
      answer: 'Yes. It helps you know if your growth plates are open, how much potential you have left, and what areas to improve.'
    }
  ];

  return (
    <div className="bg-lightblue min-h-screen flex flex-col">
      <Head>
        <title>BioAscension – Discover Your Genetic Potential</title>
        <meta name="description" content="Looksmax your face, height, and frame by understanding what your puberty + genetics are truly capable of." />
      </Head>
      {/* Navbar */}
      <header className="sticky top-0 z-30 bg-deepblue text-white shadow flex items-center justify-between px-8 py-4">
        <div className="font-extrabold text-2xl tracking-tight flex items-center gap-2">
          <span className="bg-gradient-to-r from-teal to-deepblue text-transparent bg-clip-text">BioAscension</span>
        </div>
        <nav className="hidden md:flex gap-10 text-base font-semibold mx-auto">
          <a href="#how" className="hover:text-teal transition">How It Works</a>
          <a href="#pricing" className="hover:text-teal transition">Pricing</a>
          <a href="#faq" className="hover:text-teal transition">FAQ</a>
          <a href="#contact" className="hover:text-teal transition">Contact</a>
        </nav>
        <a href="/quiz" className="bg-gradient-to-r from-teal to-deepblue text-white px-6 py-2 rounded-lg shadow font-bold hover:from-teal-400 hover:to-deepblue transition">Start Quiz</a>
      </header>

      {/* Hero Section */}
      <section className="flex justify-center items-center min-h-[80vh] p-6 md:p-12 bg-white">
        <div className="w-full max-w-6xl flex flex-col md:flex-row items-center justify-between gap-12">
          <div className="flex-1 flex flex-col justify-center items-start">
            <div className="mb-6">
              <span className="inline-block bg-teal/10 text-teal px-4 py-2 rounded-full text-sm font-semibold mb-4">
                🧬 Science-Based Analysis
              </span>
            </div>
            <h1 className="text-5xl md:text-6xl font-extrabold mb-6 leading-tight text-deepblue">
              Discover Your <span className="text-teal">Genetic Potential</span>
            </h1>
            <p className="text-xl mb-8 max-w-lg text-gray-600 leading-relaxed">
              Unlock insights into your height, facial development, and growth potential through our advanced AI-powered genetic analysis.
            </p>
            <div className="mb-8">
              <a href="/quiz" className="inline-flex items-center justify-center bg-gradient-to-r from-teal to-deepblue text-white px-8 py-4 rounded-xl shadow-lg text-lg font-bold hover:shadow-xl hover:scale-105 transition-all duration-300">
                Start Your Analysis
                <span className="ml-2">→</span>
              </a>
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <span className="mr-4">✓ 95% Accuracy</span>
              <span>✓ Instant Results</span>
            </div>
          </div>
          <div className="flex-1 flex justify-center items-center">
            <div className="relative">
              {/* Main DNA Analysis Container */}
              <div className="w-96 h-96 relative flex items-center justify-center">
                {/* Charming background elements */}
                <div className="absolute inset-0 bg-gradient-to-br from-teal/5 via-transparent to-deepblue/5 rounded-full"></div>

                {/* Elegant scanning rings */}
                <div className="absolute inset-0 rounded-full border-2 border-teal/30 animate-spin shadow-lg" style={{ animationDuration: '25s' }}></div>
                <div className="absolute inset-8 rounded-full border border-deepblue/25 animate-spin shadow-md" style={{ animationDuration: '20s', animationDirection: 'reverse' }}></div>
                <div className="absolute inset-16 rounded-full border border-teal/15 animate-spin" style={{ animationDuration: '15s' }}></div>

                {/* Magical sparkle effects */}
                {[...Array(8)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-2 h-2 bg-teal rounded-full animate-ping opacity-40"
                    style={{
                      top: `${20 + Math.sin(i * 45 * Math.PI / 180) * 30}%`,
                      left: `${50 + Math.cos(i * 45 * Math.PI / 180) * 30}%`,
                      animationDelay: `${i * 0.8}s`,
                      animationDuration: '3s'
                    }}
                  ></div>
                ))}

                {/* Central DNA Helix */}
                <div className="relative w-64 h-64 flex items-center justify-center">
                  {/* Beautiful DNA Double Helix Structure */}
                  <div className="relative w-52 h-52 dna-helix">
                    {/* Create the elegant double helix */}
                    {[...Array(20)].map((_, i) => (
                      <div
                        key={i}
                        className="absolute inset-0"
                        style={{
                          transform: `rotateZ(${i * 18}deg)`,
                          opacity: 0.7 + (i % 3) * 0.1
                        }}
                      >
                        {/* Left DNA strand with glow */}
                        <div className="absolute left-1/3 top-0 w-1.5 h-full bg-gradient-to-b from-teal via-teal/90 to-teal/50 rounded-full transform -rotate-15 shadow-lg shadow-teal/30"></div>
                        {/* Right DNA strand with glow */}
                        <div className="absolute right-1/3 top-0 w-1.5 h-full bg-gradient-to-b from-deepblue via-deepblue/90 to-deepblue/50 rounded-full transform rotate-15 shadow-lg shadow-deepblue/30"></div>

                        {/* Charming base pairs with subtle animation */}
                        {[...Array(10)].map((_, pair) => (
                          <div
                            key={pair}
                            className="absolute left-1/3 right-1/3 h-0.5 bg-gradient-to-r from-teal via-white to-deepblue rounded-full shadow-sm"
                            style={{
                              top: `${8 + pair * 8}%`,
                              opacity: 0.8 + Math.sin(pair * 0.5) * 0.2,
                              transform: `rotateZ(${pair * 12}deg) scale(${0.8 + Math.sin(pair * 0.3) * 0.2})`
                            }}
                          ></div>
                        ))}
                      </div>
                    ))}

                    {/* Enchanting central core */}
                    <div className="absolute inset-1/4 bg-gradient-to-br from-teal/30 to-deepblue/30 rounded-full backdrop-blur-sm border-2 border-white/40 shadow-2xl glow-effect"></div>
                    <div className="absolute inset-1/3 bg-gradient-to-br from-teal to-deepblue rounded-full shadow-xl animate-pulse flex items-center justify-center border-2 border-white/20">
                      <span className="text-white text-3xl animate-bounce" style={{ animationDuration: '2s' }}>🧬</span>
                    </div>
                  </div>

                  {/* Gentle scanning beam effect */}
                  <div className="absolute inset-0 pointer-events-none">
                    <div className="absolute top-0 left-1/2 w-1 h-full bg-gradient-to-b from-transparent via-teal/60 to-transparent opacity-50 animate-spin shadow-lg shadow-teal/20" style={{ animationDuration: '4s' }}></div>
                  </div>
                </div>

                {/* Charming Credibility Indicators */}
                <div className="absolute -top-6 -right-6 floating-element">
                  <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl p-5 border-2 border-teal/20 hover:border-teal/40 transition-all duration-300">
                    <div className="text-center">
                      <div className="text-4xl font-bold bg-gradient-to-r from-teal to-deepblue bg-clip-text text-transparent mb-2">95%</div>
                      <div className="text-xs text-gray-700 font-semibold">Accuracy Rate</div>
                      <div className="text-xs text-gray-500">✨ Clinically Validated</div>
                    </div>
                  </div>
                </div>

                <div className="absolute -bottom-6 -left-6 floating-element" style={{ animationDelay: '1s' }}>
                  <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl p-5 border-2 border-deepblue/20 hover:border-deepblue/40 transition-all duration-300">
                    <div className="text-center">
                      <div className="text-3xl font-bold bg-gradient-to-r from-deepblue to-teal bg-clip-text text-transparent mb-2">10,000+</div>
                      <div className="text-xs text-gray-700 font-semibold">Users Tested</div>
                      <div className="text-xs text-gray-500">🎯 Proven Results</div>
                    </div>
                  </div>
                </div>

                <div className="absolute -top-6 -left-6 floating-element" style={{ animationDelay: '2s' }}>
                  <div className="bg-gradient-to-br from-teal via-teal to-deepblue rounded-2xl shadow-2xl p-4 border border-white/30 hover:scale-110 transition-all duration-300">
                    <div className="text-center">
                      <div className="text-white text-2xl mb-2 animate-pulse">⚡</div>
                      <div className="text-white text-xs font-bold">Instant</div>
                      <div className="text-white/80 text-xs">Results</div>
                    </div>
                  </div>
                </div>

                <div className="absolute -bottom-6 -right-6 floating-element" style={{ animationDelay: '1.5s' }}>
                  <div className="bg-gradient-to-br from-deepblue via-deepblue to-teal rounded-2xl shadow-2xl p-4 border border-white/30 hover:scale-110 transition-all duration-300">
                    <div className="text-center">
                      <div className="text-white text-2xl mb-2 animate-pulse">🔬</div>
                      <div className="text-white text-xs font-bold">AI-Powered</div>
                      <div className="text-white/80 text-xs">Analysis</div>
                    </div>
                  </div>
                </div>

                {/* Delightful floating genetic particles */}
                {[...Array(15)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-2 h-2 bg-gradient-to-br from-teal to-deepblue rounded-full shadow-lg particle-orbit opacity-70 animate-pulse"
                    style={{
                      top: '50%',
                      left: '50%',
                      animationDelay: `${i * 0.4}s`,
                      animationDuration: `${10 + i * 0.5}s`
                    }}
                  ></div>
                ))}

                {/* Charming scientific indicators */}
                <div className="absolute top-6 left-6 floating-element" style={{ animationDelay: '3s' }}>
                  <div className="bg-white/90 backdrop-blur-sm rounded-xl shadow-lg px-3 py-2 border border-teal/20">
                    <div className="text-xs text-teal font-bold flex items-center">
                      <span className="w-2 h-2 bg-teal rounded-full mr-2 animate-pulse"></span>
                      DNA Analysis Active
                    </div>
                  </div>
                </div>

                <div className="absolute bottom-6 right-6 floating-element" style={{ animationDelay: '4s' }}>
                  <div className="bg-white/90 backdrop-blur-sm rounded-xl shadow-lg px-3 py-2 border border-deepblue/20">
                    <div className="text-xs text-deepblue font-bold flex items-center">
                      <span className="w-2 h-2 bg-deepblue rounded-full mr-2 animate-pulse"></span>
                      Processing Complete
                    </div>
                  </div>
                </div>

                {/* Elegant connecting constellation */}
                <div className="absolute inset-0 pointer-events-none opacity-25">
                  <svg className="w-full h-full" viewBox="0 0 400 400">
                    <circle
                      cx="200"
                      cy="200"
                      r="160"
                      stroke="url(#charmingGradient)"
                      strokeWidth="2"
                      fill="none"
                      strokeDasharray="15,10"
                      className="animate-spin"
                      style={{ animationDuration: '30s' }}
                    />
                    <circle
                      cx="200"
                      cy="200"
                      r="120"
                      stroke="url(#charmingGradient2)"
                      strokeWidth="1"
                      fill="none"
                      strokeDasharray="8,5"
                      className="animate-spin"
                      style={{ animationDuration: '20s', animationDirection: 'reverse' }}
                    />
                    <defs>
                      <linearGradient id="charmingGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" stopColor="#00C2A8" stopOpacity="0.8" />
                        <stop offset="50%" stopColor="#0A0E3F" stopOpacity="0.4" />
                        <stop offset="100%" stopColor="#00C2A8" stopOpacity="0.8" />
                      </linearGradient>
                      <linearGradient id="charmingGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" stopColor="#0A0E3F" stopOpacity="0.6" />
                        <stop offset="50%" stopColor="#00C2A8" stopOpacity="0.3" />
                        <stop offset="100%" stopColor="#0A0E3F" stopOpacity="0.6" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why BioAscension */}
      <section className="py-24 px-6 md:px-20 bg-gray-50" id="why">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block bg-teal/10 text-teal px-4 py-2 rounded-full text-sm font-semibold mb-4">
              Why Choose Us
            </span>
            <h2 className="text-4xl md:text-5xl font-extrabold text-deepblue mb-6">
              Why Choose <span className="text-teal">BioAscension</span>?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              The world's most advanced genetic potential analysis platform, trusted by thousands of users worldwide
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: '⚡',
                title: 'Instant Results',
                desc: 'Get your comprehensive report in minutes, not weeks. No lab visits or waiting periods required.',
                highlight: 'Under 10 minutes'
              },
              {
                icon: '🔬',
                title: '95% Accuracy',
                desc: 'Built on cutting-edge AI trained with thousands of real genetic and growth data points.',
                highlight: 'Science-backed'
              },
              {
                icon: '💸',
                title: 'Affordable Access',
                desc: 'Professional-grade genetic analysis starting at just $1. No expensive lab fees.',
                highlight: 'From $1.00'
              },
              {
                icon: '🧑‍🔬',
                title: 'Looksmax Optimized',
                desc: 'Specifically designed for teens and young adults focused on maximizing their genetic potential.',
                highlight: 'Age 13-25'
              },
            ].map((item, i) => (
              <div key={i} className="group relative h-full">
                <div className="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                  {/* Icon with background */}
                  <div className="w-16 h-16 bg-teal/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-teal/20 transition-colors duration-300">
                    <span className="text-3xl">{item.icon}</span>
                  </div>

                  <h3 className="text-xl font-bold mb-3 text-deepblue text-center">{item.title}</h3>

                  {/* Highlight badge */}
                  <div className="bg-teal/10 text-teal px-3 py-1 rounded-full text-sm font-semibold mb-4 border border-teal/20">
                    {item.highlight}
                  </div>

                  <p className="text-gray-600 text-center leading-relaxed text-sm flex-grow">{item.desc}</p>

                  {/* Bottom accent */}
                  <div className="absolute bottom-0 left-8 right-8 h-1 bg-gradient-to-r from-teal to-deepblue rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-24 px-6 md:px-20 bg-white" id="how">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block bg-teal/10 text-teal px-4 py-2 rounded-full text-sm font-semibold mb-4">
              Our Process
            </span>
            <h2 className="text-4xl md:text-5xl font-extrabold text-deepblue mb-6">
              How It <span className="text-teal">Works</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Our advanced AI analyzes your responses through a scientifically-backed process
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[
              {
                icon: '📝',
                title: 'Complete Assessment',
                desc: 'Answer our comprehensive 75+ question quiz covering genetics, growth patterns, and physical development markers.',
                time: '15-20 minutes',
                step: '01'
              },
              {
                icon: '📊',
                title: 'AI Analysis',
                desc: 'Our proprietary algorithms analyze your data against thousands of genetic and growth patterns to predict your potential.',
                time: 'Instant processing',
                step: '02'
              },
              {
                icon: '⚡',
                title: 'Receive Report',
                desc: 'Get your personalized genetic potential report with actionable insights delivered directly to your email.',
                time: 'Immediate delivery',
                step: '03'
              },
            ].map((item, i) => (
              <div key={i} className="relative group">
                {/* Step connector line */}
                {i < 2 && (
                  <div className="hidden md:block absolute top-20 left-full w-8 h-0.5 bg-gray-300 z-0">
                    <div className="h-full bg-gradient-to-r from-teal to-deepblue rounded-full"></div>
                  </div>
                )}

                <div className="relative z-10 bg-white rounded-2xl shadow-lg p-8 border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                  {/* Step number */}
                  <div className="absolute -top-4 -left-4 w-12 h-12 bg-gradient-to-br from-teal to-deepblue rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
                    {item.step}
                  </div>

                  {/* Icon */}
                  <div className="w-16 h-16 bg-teal/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-teal/20 transition-colors duration-300">
                    <span className="text-3xl">{item.icon}</span>
                  </div>

                  <h3 className="text-xl font-bold mb-4 text-deepblue">{item.title}</h3>

                  {/* Time badge */}
                  <div className="inline-block bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium mb-4 border border-gray-200">
                    {item.time}
                  </div>

                  <p className="text-gray-600 leading-relaxed text-sm">{item.desc}</p>

                  {/* Bottom accent */}
                  <div className="absolute bottom-0 left-8 right-8 h-1 bg-gradient-to-r from-teal to-deepblue rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </div>
            ))}
          </div>

          {/* Call to action */}
          <div className="text-center mt-16">
            <a
              href="/quiz"
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-teal to-deepblue text-white rounded-xl shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 text-lg font-bold"
            >
              Start Your Analysis
              <span className="ml-2">→</span>
            </a>
          </div>
        </div>
      </section>

      {/* Sample Predictions Dashboard */}
      <section className="py-24 px-6 md:px-20 bg-gray-50" id="dashboard">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block bg-teal/10 text-teal px-4 py-2 rounded-full text-sm font-semibold mb-4">
              Sample Report
            </span>
            <h2 className="text-4xl md:text-5xl font-extrabold text-deepblue mb-6">
              See Your <span className="text-teal">Potential</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Preview what your personalized genetic analysis report will reveal about your growth potential
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {/* Example dashboard cards */}
            {[
              {
                icon: '📏',
                title: 'Height Growth Range',
                value: '5\'8" – 6\'2"',
                progress: 70,
                subtitle: 'Estimated final range'
              },
              {
                icon: '🧪',
                title: 'Hormone Development',
                value: 'Rising Trajectory',
                chart: true,
                subtitle: 'Testosterone levels'
              },
              {
                icon: '💀',
                title: 'Facial Maturity',
                value: '50% Complete',
                progress: 50,
                subtitle: 'Bone development'
              },
              {
                icon: '⏳',
                title: 'Puberty Stage',
                value: 'Late Stage',
                progress: 80,
                subtitle: '80% complete'
              }
            ].map((item, i) => (
              <div key={i} className="group">
                <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                  {/* Icon with background */}
                  <div className="w-12 h-12 bg-teal/10 rounded-xl flex items-center justify-center mb-4 group-hover:bg-teal/20 transition-colors duration-300">
                    <span className="text-2xl">{item.icon}</span>
                  </div>

                  <h3 className="text-lg font-bold mb-4 text-deepblue">{item.title}</h3>

                  {/* Progress bar or chart */}
                  {item.progress && (
                    <div className="w-full mb-4">
                      <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-teal to-deepblue rounded-full transition-all duration-1000 ease-out"
                          style={{ width: `${item.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {item.chart && (
                    <div className="w-full h-12 flex items-end justify-start mb-4">
                      {[6, 10, 14, 12, 16].map((height, idx) => (
                        <div
                          key={idx}
                          className="w-2 bg-gradient-to-t from-teal to-deepblue mx-0.5 rounded-t transition-all duration-500 ease-out"
                          style={{
                            height: `${height * 3}px`,
                            animationDelay: `${idx * 100}ms`
                          }}
                        ></div>
                      ))}
                    </div>
                  )}

                  <div>
                    <p className="text-lg font-bold text-deepblue mb-1">{item.value}</p>
                    <p className="text-sm text-gray-600">{item.subtitle}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Additional info */}
          <div className="bg-white rounded-2xl p-8 max-w-4xl mx-auto border border-gray-200 shadow-lg">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-deepblue mb-6">Your Complete Analysis Includes</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 bg-teal/10 rounded-xl flex items-center justify-center mb-3">
                    <span className="text-2xl">🎯</span>
                  </div>
                  <span className="font-semibold text-deepblue">Personalized Predictions</span>
                  <span className="text-sm text-gray-600 mt-1">Tailored to your genetics</span>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 bg-teal/10 rounded-xl flex items-center justify-center mb-3">
                    <span className="text-2xl">📈</span>
                  </div>
                  <span className="font-semibold text-deepblue">Growth Timeline</span>
                  <span className="text-sm text-gray-600 mt-1">Month-by-month projections</span>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 bg-teal/10 rounded-xl flex items-center justify-center mb-3">
                    <span className="text-2xl">💡</span>
                  </div>
                  <span className="font-semibold text-deepblue">Optimization Tips</span>
                  <span className="text-sm text-gray-600 mt-1">Actionable recommendations</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-24 px-6 md:px-20 bg-white" id="pricing">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block bg-teal/10 text-teal px-4 py-2 rounded-full text-sm font-semibold mb-4">
              Pricing Plans
            </span>
            <h2 className="text-4xl md:text-5xl font-extrabold text-deepblue mb-6">
              Simple, <span className="text-teal">No BS</span> Pricing
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Professional genetic analysis at a fraction of traditional lab costs. Choose the plan that fits your needs.
            </p>
          </div>

          <div className="flex flex-col lg:flex-row gap-8 max-w-5xl mx-auto justify-center">
            {/* Basic Plan */}
            <div className="flex-1 relative group">
              <div className="bg-white rounded-3xl shadow-2xl p-10 flex flex-col border-2 border-gray-200 hover:border-teal hover:shadow-3xl transition-all duration-300 h-full">
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-teal/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">📊</span>
                  </div>
                  <h3 className="text-2xl font-bold mb-2 text-deepblue">Essential Report</h3>
                  <div className="text-5xl font-extrabold mb-2 text-deepblue">$1<span className="text-lg text-gray-500">.00</span></div>
                  <p className="text-gray-500">One-time payment</p>
                </div>

                <ul className="space-y-4 mb-8 flex-grow">
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span className="text-gray-700">Comprehensive height prediction analysis</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span className="text-gray-700">Puberty stage assessment</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span className="text-gray-700">Growth timeline projections</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span className="text-gray-700">Instant email delivery</span>
                  </li>
                </ul>

                <button className="w-full bg-teal text-white py-4 rounded-2xl shadow-lg hover:bg-teal-600 hover:shadow-xl font-bold text-lg transition-all duration-300 group-hover:scale-105">
                  Get Started
                </button>
              </div>
            </div>

            {/* Premium Plan */}
            <div className="flex-1 relative group">
              {/* Popular badge */}
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                <div className="bg-gradient-to-r from-teal to-deepblue text-white px-6 py-2 rounded-full text-sm font-bold shadow-lg">
                  🔥 Most Popular
                </div>
              </div>

              <div className="bg-gradient-to-br from-deepblue to-lightblue rounded-3xl shadow-2xl p-10 flex flex-col border-2 border-teal hover:shadow-3xl transition-all duration-300 h-full text-white">
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">🚀</span>
                  </div>
                  <h3 className="text-2xl font-bold mb-2">Complete Blueprint</h3>
                  <div className="text-5xl font-extrabold mb-2">$4<span className="text-lg opacity-70">.99</span></div>
                  <p className="opacity-70">One-time payment</p>
                </div>

                <ul className="space-y-4 mb-8 flex-grow">
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span>Everything in Essential Report</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span>Advanced looksmax optimization strategies</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span>Detailed facial development predictions</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span>Personalized lifestyle recommendations</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span>Hormone optimization timeline</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-teal mr-3 mt-1">✓</span>
                    <span>Genetic potential maximization guide</span>
                  </li>
                </ul>

                <button className="w-full bg-white text-deepblue py-4 rounded-2xl shadow-lg hover:bg-gray-100 hover:shadow-xl font-bold text-lg transition-all duration-300 group-hover:scale-105">
                  Get Complete Analysis
                </button>
              </div>
            </div>
          </div>

          {/* Trust indicators */}
          <div className="mt-16 text-center">
            <div className="bg-gray-50 rounded-2xl p-8 border border-gray-200">
              <h3 className="text-lg font-semibold text-deepblue mb-6">Trusted & Secure</h3>
              <div className="flex flex-col md:flex-row items-center justify-center space-y-6 md:space-y-0 md:space-x-12">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-white rounded-xl shadow-md flex items-center justify-center mr-3">
                    <svg className="w-8 h-4" viewBox="0 0 60 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M59.5759 13.5587C59.5759 12.8176 59.5759 12.0765 59.5759 11.3354C59.5759 10.5943 59.5759 9.85317 59.5759 9.11206C59.5759 8.37095 59.5759 7.62984 59.5759 6.88873C59.5759 6.14762 59.5759 5.40651 59.5759 4.6654C59.5759 3.92429 59.5759 3.18318 59.5759 2.44207C59.5759 1.70096 59.5759 0.959854 59.5759 0.218746H0.424072C0.424072 0.959854 0.424072 1.70096 0.424072 2.44207C0.424072 3.18318 0.424072 3.92429 0.424072 4.6654C0.424072 5.40651 0.424072 6.14762 0.424072 6.88873C0.424072 7.62984 0.424072 8.37095 0.424072 9.11206C0.424072 9.85317 0.424072 10.5943 0.424072 11.3354C0.424072 12.0765 0.424072 12.8176 0.424072 13.5587H59.5759Z" fill="#6772E5"/>
                      <path d="M26.3158 9.11206C26.3158 8.37095 26.3158 7.62984 26.3158 6.88873C26.3158 6.14762 26.3158 5.40651 26.3158 4.6654C26.3158 3.92429 26.3158 3.18318 26.3158 2.44207C26.3158 1.70096 26.3158 0.959854 26.3158 0.218746H33.6842C33.6842 0.959854 33.6842 1.70096 33.6842 2.44207C33.6842 3.18318 33.6842 3.92429 33.6842 4.6654C33.6842 5.40651 33.6842 6.14762 33.6842 6.88873C33.6842 7.62984 33.6842 8.37095 33.6842 9.11206H26.3158Z" fill="white"/>
                    </svg>
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-deepblue">Stripe</div>
                    <div className="text-sm text-gray-600">Secure payments</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mr-3">
                    <span className="text-2xl">🔒</span>
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-deepblue">SSL Encrypted</div>
                    <div className="text-sm text-gray-600">Bank-level security</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mr-3">
                    <span className="text-2xl">⚡</span>
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-deepblue">Instant Delivery</div>
                    <div className="text-sm text-gray-600">Results in minutes</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center mr-3">
                    <span className="text-2xl">💯</span>
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-deepblue">Guarantee</div>
                    <div className="text-sm text-gray-600">Money-back promise</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-24 px-6 md:px-20 bg-gray-50" id="faq">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block bg-teal/10 text-teal px-4 py-2 rounded-full text-sm font-semibold mb-4">
              FAQ
            </span>
            <h2 className="text-4xl md:text-5xl font-extrabold text-deepblue mb-6">
              Frequently Asked <span className="text-teal">Questions</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Get answers to common questions about our genetic analysis process
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, idx) => (
              <div key={idx} className="group">
                <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300">
                  <button
                    className="w-full text-left px-8 py-6 focus:outline-none flex justify-between items-center text-deepblue font-semibold text-lg hover:bg-gray-50 transition-all duration-300"
                    onClick={() => setOpenFaq(openFaq === idx ? null : idx)}
                    aria-expanded={openFaq === idx}
                    aria-controls={`faq-answer-${idx}`}
                  >
                    <span className="flex items-center">
                      <span className="w-8 h-8 bg-teal/10 rounded-lg flex items-center justify-center mr-4 group-hover:bg-teal/20 transition-colors duration-300">
                        <span className="text-teal font-bold text-sm">{String(idx + 1).padStart(2, '0')}</span>
                      </span>
                      {faq.question}
                    </span>
                    <div className={`ml-4 w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center transition-all duration-300 ${openFaq === idx ? 'rotate-180 bg-teal text-white' : 'group-hover:bg-gray-200'}`}>
                      <span className="text-sm">▼</span>
                    </div>
                  </button>
                  <div
                    id={`faq-answer-${idx}`}
                    className={`overflow-hidden transition-all duration-300 ease-in-out ${
                      openFaq === idx ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                    }`}
                  >
                    <div className="px-8 pb-6">
                      <div className="bg-gray-50 rounded-xl p-4 border-l-4 border-teal">
                        <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Additional help section */}
          <div className="mt-16 text-center">
            <div className="bg-white rounded-2xl p-8 border border-gray-200 shadow-lg">
              <h3 className="text-2xl font-bold text-deepblue mb-4">Still have questions?</h3>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-teal to-deepblue text-white rounded-xl shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 font-bold"
              >
                <span className="mr-2">📧</span>
                Contact Support
              </a>
            </div>
          </div>
        </div>
      </section>



      {/* Footer */}
      <footer className="bg-gray-50 border-t border-gray-200 py-12 px-6 md:px-20 mt-auto">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-6 md:mb-0">
              <div className="font-bold text-2xl text-deepblue mb-2">BioAscension</div>
              <p className="text-gray-600 text-sm">Unlock your genetic potential</p>
            </div>
            <div className="flex gap-8 text-sm mb-6 md:mb-0">
              <a href="#" className="text-gray-600 hover:text-teal transition-colors duration-300">Home</a>
              <a href="#how" className="text-gray-600 hover:text-teal transition-colors duration-300">How It Works</a>
              <a href="#pricing" className="text-gray-600 hover:text-teal transition-colors duration-300">Pricing</a>
              <a href="#faq" className="text-gray-600 hover:text-teal transition-colors duration-300">FAQ</a>
              <a href="#contact" className="text-gray-600 hover:text-teal transition-colors duration-300">Contact</a>
            </div>
            <div className="text-xs text-gray-500">© 2024 BioAscension. All rights reserved.</div>
          </div>
        </div>
      </footer>
    </div>
  );
} 